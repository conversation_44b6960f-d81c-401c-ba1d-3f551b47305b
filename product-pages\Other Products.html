﻿<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="description" content="Browse our comprehensive range of pharmaceutical products. EDEN Health Care offers tablets, capsules, suspensions, and solutions for various therapeutic needs including diabetes, antifungal, and antiparasitic treatments.">
<title>
Other Products - EDEN Health Care
</title>
<!-- Preload critical resources -->
<link rel="preconnect" href="https://cdnjs.cloudflare.com">
<link rel="preconnect" href="https://images.unsplash.com">
<!-- Optimized CSS -->
<link rel="stylesheet" href="../css/optimized.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" media="print" onload="this.media='all'">
<noscript>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</noscript>
<!-- Custom page styles -->
<style>
/* Banner styles */ .products-hero { background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1631549916768-4119b4123a21?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=80'); } /* Download section styles */ .download-section { background-color: #f9fbfd; padding: 3rem 0; text-align: center; } .download-content { max-width: 800px; margin: 0 auto; } .download-content h2 { margin-bottom: 1rem; color: #2c3e50; } .download-content p { margin-bottom: 2rem; color: #555; } .download-btn { display: inline-flex; align-items: center; justify-content: center; gap: 0.8rem; background-color: #0056b3; color: white; padding: 1rem 2rem; border-radius: 4px; text-decoration: none; font-weight: 600; transition: all 0.3s ease; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); } .download-btn:hover { background-color: #003d7a; transform: translateY(-2px); } .download-btn i { font-size: 1.2rem; } /* Lightbox styles */ .product-image { cursor: pointer; position: relative; overflow: hidden; } .product-image::after { content: "\f00e"; font-family: "Font Awesome 5 Free"; font-weight: 900; position: absolute; top: 10px; right: 10px; background-color: rgba(0, 0, 0, 0.5); color: white; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 14px; opacity: 0; transition: opacity 0.3s ease; } .product-image:hover::after { opacity: 1; } .lightbox { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.9); display: flex; align-items: center; justify-content: center; z-index: 9999; opacity: 0; visibility: hidden; transition: opacity 0.3s ease, visibility 0.3s ease; } .lightbox.active { opacity: 1; visibility: visible; } .lightbox-content { position: relative; max-width: 90%; max-height: 90%; } .lightbox-image { display: block; max-width: 100%; max-height: 90vh; box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5); } .lightbox-close { position: absolute; top: -40px; right: 0; width: 30px; height: 30px; background-color: transparent; border: none; color: white; font-size: 24px; cursor: pointer; display: flex; align-items: center; justify-content: center; } .lightbox-caption { position: absolute; bottom: -40px; left: 0; width: 100%; color: white; text-align: center; font-size: 16px; padding: 10px 0; } /* Footer Dropdown Menu Styles */ .footer-dropdown { position: relative; } .footer-dropdown-toggle { display: flex; align-items: center; justify-content: space-between; width: 100%; padding: 12px 16px; background-color: rgba(255, 255, 255, 0.1); border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 8px; color: white; font-size: 0.95rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; text-align: left; } .footer-dropdown-toggle:hover { background-color: rgba(255, 255, 255, 0.2); border-color: var(--accent-color); } .footer-dropdown-toggle:focus { outline: none; border-color: var(--accent-color); box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.3); } .footer-dropdown-toggle i { margin-left: 8px; transition: transform 0.3s ease; font-size: 0.9rem; } .footer-dropdown.open .footer-dropdown-toggle i { transform: rotate(180deg); } .footer-dropdown-menu { position: absolute; top: 100%; left: 0; right: 0; background-color: rgba(255, 255, 255, 0.95); border-radius: 8px; box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3); backdrop-filter: blur(10px); max-height: 0; overflow: hidden; transition: all 0.3s ease; z-index: 1000; margin-top: 8px; } .footer-dropdown.open .footer-dropdown-menu { max-height: 300px; overflow-y: auto; } .footer-dropdown-menu a { display: block; padding: 12px 16px; color: #2c3e50; text-decoration: none; font-size: 0.9rem; transition: all 0.3s ease; border-left: 3px solid transparent; } .footer-dropdown-menu a:hover { background-color: rgba(52, 152, 219, 0.1); color: var(--accent-color); border-left-color: var(--accent-color); padding-left: 20px; } .footer-dropdown-menu a.active { background-color: var(--accent-color); color: white; border-left-color: #2c3e50; font-weight: 600; } .footer-dropdown-menu a.active:hover { background-color: #2980b9; color: white; padding-left: 16px; } /* Responsive styles for dropdown */ @media (max-width: 768px) { .footer-dropdown-toggle { padding: 10px 14px; font-size: 0.9rem; } .footer-dropdown-menu a { padding: 10px 14px; font-size: 0.85rem; } .footer-dropdown-menu a:hover { padding-left: 18px; } } @media (max-width: 480px) { .footer-dropdown-toggle { padding: 8px 12px; font-size: 0.85rem; } .footer-dropdown-menu { max-height: 250px; } .footer-dropdown.open .footer-dropdown-menu { max-height: 250px; } .footer-dropdown-menu a { padding: 8px 12px; font-size: 0.8rem; } .footer-dropdown-menu a:hover { padding-left: 16px; } }
</style>
</head>
<body>
<div class="wrapper">
<header>
<nav class="main-nav">
<div class="nav-container">
<button class="mobile-menu-btn" aria-label="Toggle menu">
<i class="fas fa-bars">
</i>
</button>
<div class="logo">
<img alt="EDEN Pharmaceuticals" />
</div>
<ul class="nav-links">
<li>
<a href="../index.html">
Home
</a>
</li>
<li>
<a href="../about.html">
About
</a>
</li>
<li>
<a href="../plant.html">
Our Plant
</a>
</li>
<li class="active dropdown">
<a href="../products.html">
Products
</a>
<ul class="dropdown-menu">
<li>
<a href="Anti-Cold Anti-Histaminic and Anti-Allergic.html">
Anti-Cold, Anti-Histaminic and Anti-Allergic
</a>
</li>
<li>
<a href="Digestive Preparations and Antacids.html">
Digestive Preparations and Antacids
</a>
</li>
<li>
<a href="Ayurvedic.html">
Ayurvedic Products
</a>
</li>
<li>
<a href="External Preparations.html">
External Preparations
</a>
</li>
<li>
<a href="Injectables.html">
Injectables
</a>
</li>
<li>
<a href="Nutritional Supplements and Haematinics.html">
Nutritional Supplements and Haematinics
</a>
</li>
<li>
<a href="Antibiotics.html">
Antibiotics
</a>
</li>
<li>
<a href="Analgesic, Anti-Inflammatory and Anti-Arthritis.html">
Analgesic, Anti-inflammatory and Anti Arthritis
</a>
</li>
<li>
<a href="Other Products.html">
Other Products
</a>
</li>
</ul>
</li>
<li>
<a class="contact-btn" href="../contact.html">
Contact Us
</a>
</li>
</ul>
</div>
</nav>
</header>
<main>
<!-- Hero Banner Section -->
<section class="products-hero">
<div class="products-hero-content">
<h1>
Other Products
</h1>
<p>
Comprehensive pharmaceutical solutions for diverse healthcare needs
</p>
</div>
</section>
<!-- Product Gallery Section -->
<section class="products-gallery">
<div class="container">
<div class="section-header">
<h2>
Pharmaceutical
<span class="highlight-text">
Products
</span>
</h2>
<p class="section-subtitle">
Browse our comprehensive range of pharmaceutical products for various therapeutic applications
</p>
</div>
<div class="product-grid">
<!-- Product 1: DOXYDEN TABS -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="eager" alt="DOXYDEN TABS">
</div>
<div class="product-content">
<h3>
DOXYDEN TABS
</h3>
<p>
Doxylamine Succinate 10mg + Pyridoxine HCL 10mg + Folic Acid 2.5mg
</p>
</div>
</div>
</div>
<!-- Product 2: EMERON 4 TABS -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="eager" alt="EMERON 4 TABS">
</div>
<div class="product-content">
<h3>
EMERON 4 TABS
</h3>
<p>
Ondansetron 4mg (Mouth Dissolving)
</p>
</div>
</div>
</div>
<!-- Product 3: EMERON SYRUP -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="EMERON SYRUP">
</div>
<div class="product-content">
<h3>
EMERON SYRUP
</h3>
<p>
Ondansetron 2mg/5ML
</p>
</div>
</div>
</div>
<!-- Product 4: ALEDEN SUSPENSION -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="ALEDEN SUSPENSION">
</div>
<div class="product-content">
<h3>
ALEDEN SUSPENSION
</h3>
<p>
Albendazole 200mg/5ML
</p>
</div>
</div>
</div>
<!-- Product 5: ALEDEN TABLET -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="ALEDEN TABLET">
</div>
<div class="product-content">
<h3>
ALEDEN TABLET
</h3>
<p>
Albendazole 400mg
</p>
</div>
</div>
</div>
<!-- Product 6: ALEDEN PLUS TABLET -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="ALEDEN PLUS TABLET">
</div>
<div class="product-content">
<h3>
ALEDEN PLUS TABLET
</h3>
<p>
Albendazole 400mg + Ivermectin 6mg
</p>
</div>
</div>
</div>
<!-- Product 7: ALEDEN-PLUS SUSP -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="ALEDEN-PLUS SUSP">
</div>
<div class="product-content">
<h3>
ALEDEN-PLUS SUSP
</h3>
<p>
Albendazole 200mg + Ivermectin 3mg/5ml
</p>
</div>
</div>
</div>
<!-- Product 8: CONSTIDEN SUSP -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="CONSTIDEN SUSP">
</div>
<div class="product-content">
<h3>
CONSTIDEN SUSP
</h3>
<p>
Liquid Paraffin, Milk of Magnesia & Sodium Picosulphate
</p>
</div>
</div>
</div>
<!-- Product 9: DENLAX SOLUTION -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="DENLAX SOLUTION">
</div>
<div class="product-content">
<h3>
DENLAX SOLUTION
</h3>
<p>
LACTULOSE 10GM/15ML
</p>
</div>
</div>
</div>
<!-- Product 10: EFZOLE 150 TABS -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="EFZOLE 150 TABS">
</div>
<div class="product-content">
<h3>
EFZOLE 150 TABS
</h3>
<p>
Fluconazole 150mg
</p>
</div>
</div>
</div>
<!-- Product 11: GLIMIDEN - M1 TAB -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="GLIMIDEN - M1 TAB">
</div>
<div class="product-content">
<h3>
GLIMIDEN - M1 TAB
</h3>
<p>
GLIMEPIRIDE 1MG + METFORMIN HCL 500MG
</p>
</div>
</div>
</div>
<!-- Product 12: GLIMIDEN - M2 TAB -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="GLIMIDEN - M2 TAB">
</div>
<div class="product-content">
<h3>
GLIMIDEN - M2 TAB
</h3>
<p>
GLIMEPIRIDE 2MG + METFORMIN HCL 500MG
</p>
</div>
</div>
</div>
<!-- Product 13: ITCOZOLE 100 CAPSULE -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="ITCOZOLE 100 CAPSULE">
</div>
<div class="product-content">
<h3>
ITCOZOLE 100 CAPSULE
</h3>
<p>
Itraconazole 100mg
</p>
</div>
</div>
</div>
<!-- Product 14: ITCOZOLE 200 CAPSULE -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="ITCOZOLE 200 CAPSULE">
</div>
<div class="product-content">
<h3>
ITCOZOLE 200 CAPSULE
</h3>
<p>
Itraconazole 200mg
</p>
</div>
</div>
</div>
<!-- Product 15: PREGADEN-NT TAB -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="PREGADEN-NT TAB">
</div>
<div class="product-content">
<h3>
PREGADEN-NT TAB
</h3>
<p>
PREGABALIN 75MG + NORTRIPTYLINE 10MG
</p>
</div>
</div>
</div>
<!-- Product 16: TINIDEN M TAB -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="TINIDEN M TAB">
</div>
<div class="product-content">
<h3>
TINIDEN M TAB
</h3>
<p>
TENELIGLIPTIN 20MG + METFORMIN 500MG
</p>
</div>
</div>
</div>
<!-- Product 17: TINIDEN 20 TAB -->
<div class="product-item">
<div class="product-card">
<div class="product-image">
<img width="600" height="400" loading="lazy" alt="TINIDEN 20 TAB">
</div>
<div class="product-content">
<h3>
TINIDEN 20 TAB
</h3>
<p>
TENELIGLIPTIN 20MG
</p>
</div>
</div>
</div>
</div>
</div>
</section>
<!-- Download Section -->
<section class="download-section">
<div class="container">
<div class="download-content">
<h2>
Product Information
</h2>
<p>
Download our complete product list with detailed specifications and pricing information.
</p>
<a href="product-list.pdf" class="download-btn" download="EDKEM-Product-List.pdf">
<i class="fas fa-download">
</i>
Download Product List (PDF)
</a>
</div>
</div>
</section>
</main>
<!-- Footer -->
<footer id="contact">
<div class="footer-content">
<div class="footer-nav">
<div class="footer-col">
<h3>
Map
</h3>
<!-- Map will be added here -->
<iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3672.832211154431!2d72.5360038250273!3d22.993196717424333!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x395e853ed21ede61%3A0xbbafb667025fe1b1!2sSAHAJ%20SOLITAIRE%2C%20Vishala%2C%20Ahmedabad%2C%20Gujarat%20380007!5e0!3m2!1sen!2sin!4v1746958440405!5m2!1sen!2sin" width="300" height="200" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade">
</iframe>
</div>
<div class="footer-col">
<h3>
Product Categories
</h3>
<div class="footer-dropdown">
<button class="footer-dropdown-toggle" type="button" aria-expanded="false">
Browse Products
<i class="fas fa-chevron-down">
</i>
</button>
<div class="footer-dropdown-menu">
<a href="Anti-Cold Anti-Histaminic and Anti-Allergic.html">
Anti-Cold, Anti-Histaminic and Anti-Allergic
</a>
<a href="Digestive Preparations and Antacids.html">
Digestive Preparations and Antacids
</a>
<a href="Ayurvedic.html">
Ayurvedic Products
</a>
<a href="External Preparations.html">
External Preparations
</a>
<a href="Injectables.html">
Injectables
</a>
<a href="Nutritional Supplements and Haematinics.html">
Nutritional Supplements and Haematinics
</a>
<a href="Antibiotics.html">
Antibiotics
</a>
<a href="Analgesic, Anti-Inflammatory and Anti-Arthritis.html">
Analgesic, Anti-inflammatory and Anti Arthritis
</a>
<a href="Other Products.html" class="active">
Other Products
</a>
</div>
</div>
</div>
<div class="footer-col">
<h3>
Our Office
</h3>
<p>
301, Abhishree Complex, Opp Star Bazar
</p>
<p>
Satellite Road, Ahmedabad 12345
</p>
<p>
Phone: +91 9824023088
</p>
<p>
Mobile: +91 7016386329
</p>
<p>
Email: <EMAIL>
</p>
</div>
<div class="footer-col">
<h3>
Business Hours
</h3>
<p>
Monday - Saturday: 9:00 AM - 5:00 PM
</p>
<p>
Sunday: Closed
</p>
<p>
We respond to emails within 24 hours during business days.
</p>
</div>
</div>
<div class="copyright">
<p>
Copyright Â© 2025 EDEN. All rights reserved.
</p>
<div class="social-icons">
<a href="#" class="social-icon">
<i class="fab fa-facebook-f">
</i>
</a>
<a href="#" class="social-icon">
<i class="fab fa-twitter">
</i>
</a>
<a href="#" class="social-icon">
<i class="fab fa-linkedin-in">
</i>
</a>
<a href="#" class="social-icon">
<i class="fab fa-instagram">
</i>
</a>
</div>
</div>
</div>
</footer>
<a href="#" class="back-to-top" aria-label="Back to top">
<i class="fas fa-arrow-up">
</i>
</a>
</div>
<!-- Lightbox Container -->
<div class="lightbox" id="productLightbox">
<div class="lightbox-content">
<button class="lightbox-close" id="lightboxClose">
<i class="fas fa-times">
</i>
</button>
<img alt="" class="lightbox-image" id="lightboxImage">
<div class="lightbox-caption" id="lightboxCaption">
</div>
</div>
</div>
<!-- Optimized JavaScript -->
<script src="../js/optimized.js" defer>
</script>
<!-- Lightbox JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() { // Initialize lightbox functionality initLightbox(); // Initialize products dropdown initProductsDropdown(); }); function initLightbox() { const productImages = document.querySelectorAll('.product-image img'); const lightbox = document.getElementById('productLightbox'); const lightboxImage = document.getElementById('lightboxImage'); const lightboxCaption = document.getElementById('lightboxCaption'); const lightboxClose = document.getElementById('lightboxClose'); // Add click event to all product images productImages.forEach(image =>
{ image.addEventListener('click', function() { // Get the high-resolution image URL (remove size constraints) let fullSizeUrl = this.src.replace(/&w=\d+&q=\d+/, ''); // Set the image source and alt text lightboxImage.src = fullSizeUrl; lightboxImage.alt = this.alt; // Set the caption text const productTitle = this.closest('.product-card').querySelector('h3').textContent; const productDesc = this.closest('.product-card').querySelector('p').textContent; lightboxCaption.textContent = `${productTitle} - ${productDesc}`; // Show the lightbox lightbox.classList.add('active'); // Prevent scrolling on the body document.body.style.overflow = 'hidden'; }); }); // Close lightbox when clicking the close button lightboxClose.addEventListener('click', closeLightbox); // Close lightbox when clicking outside the image lightbox.addEventListener('click', function(e) { if (e.target === lightbox) { closeLightbox(); } }); // Close lightbox when pressing Escape key document.addEventListener('keydown', function(e) { if (e.key === 'Escape' && lightbox.classList.contains('active')) { closeLightbox(); } }); function closeLightbox() { lightbox.classList.remove('active'); // Re-enable scrolling on the body document.body.style.overflow = ''; // Clear the image source after transition (for better memory management) setTimeout(() =>
{ if (!lightbox.classList.contains('active')) { lightboxImage.src = ''; } }, 300); } } function initProductsDropdown() { const dropdown = document.querySelector('.footer-dropdown'); const toggle = document.querySelector('.footer-dropdown-toggle'); const menu = document.querySelector('.footer-dropdown-menu'); if (!dropdown || !toggle || !menu) return; // Toggle dropdown on click toggle.addEventListener('click', function(e) { e.preventDefault(); e.stopPropagation(); const isOpen = dropdown.classList.contains('open'); // Close all other dropdowns first document.querySelectorAll('.footer-dropdown.open').forEach(otherDropdown =>
{ if (otherDropdown !== dropdown) { otherDropdown.classList.remove('open'); const otherToggle = otherDropdown.querySelector('.footer-dropdown-toggle'); if (otherToggle) { otherToggle.setAttribute('aria-expanded', 'false'); } } }); // Toggle current dropdown if (isOpen) { dropdown.classList.remove('open'); toggle.setAttribute('aria-expanded', 'false'); } else { dropdown.classList.add('open'); toggle.setAttribute('aria-expanded', 'true'); } }); // Close dropdown when clicking outside document.addEventListener('click', function(e) { if (!dropdown.contains(e.target)) { dropdown.classList.remove('open'); toggle.setAttribute('aria-expanded', 'false'); } }); // Close dropdown on escape key document.addEventListener('keydown', function(e) { if (e.key === 'Escape' && dropdown.classList.contains('open')) { dropdown.classList.remove('open'); toggle.setAttribute('aria-expanded', 'false'); toggle.focus(); } }); // Handle keyboard navigation within dropdown toggle.addEventListener('keydown', function(e) { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); toggle.click(); } else if (e.key === 'ArrowDown') { e.preventDefault(); if (!dropdown.classList.contains('open')) { toggle.click(); } // Focus first menu item const firstItem = menu.querySelector('a'); if (firstItem) firstItem.focus(); } }); // Handle keyboard navigation within menu const menuItems = menu.querySelectorAll('a'); menuItems.forEach((item, index) =>
{ item.addEventListener('keydown', function(e) { if (e.key === 'ArrowDown') { e.preventDefault(); const nextItem = menuItems[index + 1] || menuItems[0]; nextItem.focus(); } else if (e.key === 'ArrowUp') { e.preventDefault(); const prevItem = menuItems[index - 1] || menuItems[menuItems.length - 1]; prevItem.focus(); } else if (e.key === 'Escape') { e.preventDefault(); dropdown.classList.remove('open'); toggle.setAttribute('aria-expanded', 'false'); toggle.focus(); } }); }); }
</script>
</body>
</html>
